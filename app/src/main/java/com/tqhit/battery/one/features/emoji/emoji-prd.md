## Product Requirements Document: Emoji Battery

**Version:** 1.0
**Date:** June 18, 2025
**Author:** DucPH
**Status:** Proposed

### 1. Overview

The Emoji Battery is a user-facing personalization feature that allows users to replace their standard Android status bar battery icon with a dynamic, customizable, and visually engaging emoji/character-based indicator. This feature aims to increase user engagement and provide a unique personalization option that sets our app apart, creating a more delightful user experience.

### 2. Goals and Objectives

*   **Primary Goal:** Increase daily active users (DAU) and session duration by providing a compelling and "sticky" personalization feature.
*   **Secondary Goal:** Create a new revenue stream through premium/exclusive battery styles.
*   **User Goal:** Allow users to express their personality and style by deeply customizing a core UI element of their phone.

### 3. Target Audience

Android users who enjoy UI customization, aesthetics, and personalization. This includes, but is not limited to:
*   Younger demographics (teens, young adults) who are fans of pop culture characters (e.g., Sanrio).
*   "Power users" who want to modify every aspect of their phone's appearance.
*   Users looking for a more fun and less utilitarian mobile experience.

### 4. User Stories

*   **As a new user, I want to** browse a gallery of different emoji battery styles so that I can find one that matches my taste.
*   **As a creative user, I want to** customize the selected style by changing the character, battery appearance, and text so that I can create a unique look.
*   **As any user, I want to** see a live preview of my changes before applying them so I know exactly how it will look.
*   **As a user with the feature enabled, I want to** still be able to access my notifications and control center by swiping down from the top so that I don't lose core phone functionality.
*   **As a user, I want to** easily enable or disable the entire feature with a single toggle so I can switch back to the default system UI whenever I want.
*   **As a free user, I want to** have access to a good selection of free styles, with the option to unlock premium styles (e.g., by watching an ad or paying).
*   **As a user, I want to** discover new and seasonal emoji battery styles without having to update the app, so I always have fresh options to choose from.


### 5. Functional Requirements

#### 5.1. Main Screen (Battery Gallery)

*   **Layout:** A grid-based view showcasing available Emoji Battery styles.
*   **Categorization:** Styles will be organized into filterable tabs (e.g., 🔥 HOT, Character, Heart, Cute, etc.). The "HOT" category should feature popular or trending styles.
*   **Global Toggle:** A prominent toggle switch at the top to "Enable or disable the emoji battery" feature globally.
*   **Style Indicators:**
    *   Each item in the grid is a self-contained preview of the style.
    *   Premium styles must be clearly marked with a "premium" icon (e.g., the diamond 💎 shown in the screenshots).
*   **Navigation:** Tapping on any style (free or premium) navigates the user to the Customization Screen.

#### 5.2. Customization Screen

*   **Live Preview:** A large preview area at the top of the screen that updates in real-time as the user changes customization options. It should show the emoji/character, the battery graphic, and the percentage text at a sample level (e.g., 50%).
*   **Style Selection:**
    *   **Battery:** A horizontal scrollable list of different battery container styles.
    *   **Emoji:** A horizontal scrollable list of different emojis/characters that can be paired with the selected battery.
    *   Selections should be visually highlighted. Premium options must be marked. If a premium option is selected, a purchase/unlock flow should be initiated upon tapping "Apply".
*   **Customization Toggles:**
    *   `Show Emoji`: Toggles the visibility of the character/emoji.
    *   `Show battery percentage`: Toggles the visibility of the numerical percentage text.
*   **Customization Sliders:**
    *   `Percentage`: Adjusts the font size of the percentage text (e.g., range from 5dp to 40dp).
    *   `Emoji Battery`: Adjusts the size of the emoji/character relative to the battery graphic.
*   **Color Picker:**
    *   `Color percentage`: A button that opens a color palette to allow the user to change the color of the percentage text.
*   **Apply Button:** A prominent "Apply" button at the bottom. Tapping this button saves the configuration and activates the Overlay UI.

#### 5.3. The Overlay UI (The active Emoji Battery)

*   **Display:** When enabled, the feature will display a custom UI overlay at the top of the screen, covering the native Android status bar.
*   **Content:** The overlay will display the user's configured Emoji Battery style. The battery level and character animation must dynamically reflect the phone's actual battery percentage.
*   **Interaction (CRITICAL):** The overlay must not block access to the system's Notification Center and Control Center (Quick Settings).
    *   **Requirement:** A downward swipe gesture anywhere on the overlay UI must trigger the standard system behavior of pulling down the notification shade.
*   ✨ **Implementation Strategy:**
    *   For devices running **Android 8.0 (API 26) or higher**, the overlay will be implemented using `WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY`. This binds the overlay's permission to the Accessibility Service itself.
    *   For devices running **versions below Android 8.0**, the overlay will require the `SYSTEM_ALERT_WINDOW` permission in addition to the Accessibility Service.

#### 5.4. Permissions

The app must request the following sensitive permissions with clear, user-friendly explanations, using a conditional approach based on the Android OS version.

1.  **Accessibility Service (Required for all versions):**
    *   **Justification:** "To display the emoji battery at the top of your screen and to detect swipes to open your notification panel." This is the core permission for the feature to function.

2.  **Display Over Other Apps (`SYSTEM_ALERT_WINDOW`) (Conditionally Required):**
    *   **Requirement:** This permission will **only** be requested on devices running Android versions **below 8.0 (API 26)**.
    *   **Justification:** "On your version of Android, this permission is needed to show the emoji battery on top of other apps."

#### ✨ 5.5. Content Management and Delivery
This section outlines the strategy for loading and managing the lists of available emojis, battery styles, and their associated properties (e.g., categories, premium status).

*   **Primary Source - Firebase Remote Config:**
    *   The complete list of emoji battery styles, including their image URLs, names, categories, and premium status, will be fetched from Firebase Remote Config on app launch.
    *   **Benefits:** This allows the product team to add new content, run A/B tests on styles, feature seasonal/promotional items, and change categories without requiring users to update the application through the Play Store.

*   **Fallback Mechanism - Local JSON:**
    *   The application must bundle a local `emoji-battery-styles.json` file with a default set of emoji battery styles.
    *   This local file will be used as an immediate fallback if:
        *   The Firebase Remote Config fetch fails due to network errors.
        *   The fetch times out (e.g., takes longer than 3 seconds).
        *   The user is offline.
    *   This ensures the feature is always functional and provides a seamless user experience even without an internet connection. The bundled JSON should be updated with each new app release.

*   **Data Schema Consistency:**
    *   The data structure (schema) of the content defined in Firebase Remote Config must be identical to the structure of the local `emoji-battery-styles.json` file. This is critical for the fallback mechanism to work without errors.

*   **Caching Strategy:**
    *   The system should leverage the built-in caching capabilities of the Firebase Remote Config SDK. On subsequent app launches, the cached configuration will be loaded instantly for a fast user experience, while a fresh fetch is initiated in the background.


### 6. Monetization

*   **Banner Ads:** A banner ad will be displayed at the bottom of the main gallery and customization screens.
*   **Premium Content (Freemium Model):**
    *   A subset of battery styles and emojis/characters will be marked as "Premium".
    *   **Unlock Mechanism:** Users can unlock premium content via:
        *   In-app purchase (e.g., "Unlock all" pack).
        *   Watching a rewarded video ad to unlock a single item for a limited time or permanently.
*   **"ADS ON/OFF" Toggle:** This suggests an ad-free subscription or one-time purchase to remove all ads from the app.

### 7. Success Metrics

*   **Adoption Rate:** Percentage of users who enable the Emoji Battery feature at least once.
*   **Engagement:** Average number of style changes and customizations per user per week.
*   **Retention:** 7-day and 30-day retention rate for users who have the feature enabled.
*   **Monetization:**
    *   Conversion rate from free to premium users.
    *   Revenue generated from premium content and ad removal.
    *   Number of rewarded ads watched per DAU.

### 8. Out of Scope for V1.0

*   Replicating all other status bar icons (Wi-Fi, network signal, time, notification icons). V1.0 will focus solely on replacing the battery indicator and providing the swipe-down gesture. The native status bar will be completely hidden by the overlay.
*   User-generated content (uploading custom emojis/styles).
*   Complex charging animations (V1.0 will show a static charging state or simple animation).
*   Integration with system themes (light/dark mode).

## Implementation Plan

This plan is divided into lean, independently compilable, and testable phases. It follows Clean Architecture (Presentation, Domain, Data layers), MVI, and the specified coding conventions. 

### Phase 1: Core Data Models & Data Layer Foundation

**Goal:** Establish the data foundation. This phase focuses on defining the data structures and creating the repository responsible for fetching battery styles from both a remote source and a local fallback. This entire phase can be built and unit-tested without any UI.

*   **Task 1.1: Define Domain Layer Models**
    *   **File:** `domain/model/battery_style.kt`
        ```kotlin
data class BatteryStyle(
            val id: String,
            val name: String,
            val emojiImageUrl: String,
            val batteryImageUrl: String,
            val isPremium: Boolean
        )
    ```
    *   **File:** `domain/model/battery_style_category.kt`
        ```kotlin
data class BatteryStyleCategory(
            val name: String,
            val styles: List<BatteryStyle>
        )
    ```
    *   **Rationale:** Create immutable data classes to represent the core business objects. These are pure Kotlin and have no framework dependencies.

*   **Task 1.2: Define Domain Layer Repository Interface**
    *   **File:** `domain/repository/battery_style_repository.kt`
        ```kotlin
interface BatteryStyleRepository {
            suspend fun getBatteryStyleCategories(): Result<List<BatteryStyleCategory>>
        }
    ```
    *   **Rationale:** Defines the contract for the data layer. The Domain layer only knows about this interface, adhering to the Dependency Inversion Principle. The `Result` wrapper provides a standardized way to handle success or failure.

*   **Task 1.3: Implement Data Layer Repository**
    *   **File:** `data/repository/battery_style_repository_impl.kt`
    *   **Details:** This class will implement `BatteryStyleRepository`. It will require two data sources: one for Firebase Remote Config and one for the local JSON asset.
        1.  First, it attempts to fetch and parse the JSON configuration from Firebase Remote Config.
        2.  If the fetch fails (network error, timeout) or returns an empty/invalid config, it logs the error and proceeds to the fallback.
        3.  The fallback mechanism reads a bundled `battery_styles.json` file from the `assets` folder.
        4.  It will use a JSON parsing library (like `Gson` or `kotlinx.serialization`) to map the raw JSON into Data Transfer Objects (DTOs), which are then mapped to the `BatteryStyleCategory` domain models.
    *   **Dependencies:** Firebase Remote Config SDK, `Context` for accessing assets.

*   **Task 1.4: Create Local Fallback Asset**
    *   **File:** `app/src/main/assets/battery_styles.json`
    *   **Details:** Create a well-structured JSON file containing a default list of categories and battery styles. This file's schema must match the one expected from Firebase Remote Config.

*   **Task 1.5: Unit Testing**
    *   **File:** `data/repository/battery_style_repository_impl_test.kt`
    *   **Details:** Write unit tests for `BatteryStyleRepositoryImpl`.
        *   Test the "happy path" where data is successfully fetched from a mocked Firebase Remote Config.
        *   Test the fallback path where the mocked Firebase fetch fails, and the repository correctly loads data from a mocked local asset.

---

### Phase 2: Displaying Battery Styles (Gallery Screen)

**Goal:** Build the UI to display the battery styles fetched in Phase 1. The user will be able to see the categorized lists of styles.

*   **Task 2.1: Create Domain Use Case**
    *   **File:** `domain/use_case/get_battery_styles_use_case.kt`
        ```kotlin
class GetBatteryStylesUseCase(
            private val batteryStyleRepository: BatteryStyleRepository
        ) {
            suspend fun execute(): Result<List<BatteryStyleCategory>> {
                return batteryStyleRepository.getBatteryStyleCategories()
            }
        }
    ```
    *   **Rationale:** Encapsulates a single piece of business logic. ViewModels will interact with Use Cases, not directly with repositories.

*   **Task 2.2: Implement Battery Gallery MVI Components**
    *   **File:** `presentation/gallery/battery_gallery_state.kt`
        ```kotlin
data class BatteryGalleryState(
            val isLoading: Boolean = true,
            val categories: List<BatteryStyleCategory> = emptyList(),
            val hasError: Boolean = false,
            val errorMessage: String? = null
        )
```
    *   **File:** `presentation/gallery/battery_gallery_event.kt`
        ```kotlin
sealed class BatteryGalleryEvent {
            data class OnStyleSelected(val styleId: String) : BatteryGalleryEvent()
            object OnRetryClicked : BatteryGalleryEvent()
        }
```
    *   **Rationale:** Defines the state the UI can render and the events the UI can produce.

*   **Task 2.3: Implement Battery Gallery ViewModel**
    *   **File:** `presentation/gallery/battery_gallery_view_model.kt`
    *   **Details:**
        *   Injects `GetBatteryStylesUseCase`.
        *   Exposes a `StateFlow<BatteryGalleryState>` for the Fragment to observe.
        *   Has a function like `processIntent(event: BatteryGalleryEvent)` to handle user actions.
        *   On initialization, it executes the use case and updates the state flow with loading, success (with data), or error states.

*   **Task 2.4: Create Battery Gallery Fragment and Layout**
    *   **File:** `presentation/gallery/battery_gallery_fragment.kt`
    *   **File:** `res/layout/fragment_battery_gallery.xml`
    *   **Details:**
        *   Uses `ViewBinding`.
        *   The layout will contain a `TabLayout` for categories and a `ViewPager2` or a `RecyclerView` to display the grid of styles.
        *   The Fragment observes the `ViewModel`'s state and updates the UI accordingly (shows a progress bar, the list, or an error message).
        *   User interactions (tapping a style) are sent to the `ViewModel` as events.

*   **Task 2.5: Create RecyclerView Adapter**
    *   **File:** `presentation/gallery/adapter/battery_style_adapter.kt`
    *   **Details:** A standard `RecyclerView.Adapter` to bind `BatteryStyle` data to the item layout (`item_battery_style.xml`). It will use an image loading library like Glide or Coil to load the `emojiImageUrl` and `batteryImageUrl`. It should also display the premium icon (💎) if `isPremium` is true.

---

### Phase 3: Style Customization Screen

**Goal:** Allow users to navigate to a detail screen to customize a selected style and save their preferences.

*   **Task 3.1: Define and Implement Customization Persistence**
    *   **File:** `domain/model/customization_config.kt`
    *   **File:** `domain/repository/customization_repository.kt`
    *   **File:** `data/repository/customization_repository_impl.kt`
    *   **Details:**
        *   Define a `CustomizationConfig` data class to hold all user choices (selected style ID, colors, sizes, toggle states).
        *   Create the repository interface and implementation to save and retrieve this configuration. Use **Jetpack DataStore** for persistence, as it is the modern replacement for `SharedPreferences` and works seamlessly with `Flow`.

*   **Task 3.2: Create Customization Use Cases**
    *   **File:** `domain/use_case/save_customization_use_case.kt`
    *   **File:** `domain/use_case/load_customization_use_case.kt`
    *   **Details:** Simple use cases that call the corresponding methods on the `CustomizationRepository`.

*   **Task 3.3: Implement Customize Screen MVI, ViewModel, and Fragment**
    *   **Files:** `presentation/customize/*`
    *   **Details:**
        *   Create the `CustomizeState`, `CustomizeEvent`, and `CustomizeViewModel`.
        *   The `ViewModel` will load the current configuration and the details of the selected style.
        *   The `CustomizeFragment` will contain the large preview, sliders, toggles, and color pickers.
        *   Changes in the UI update a temporary state in the ViewModel, which is reflected in the live preview.
        *   The "Apply" button triggers an event to the ViewModel, which then uses the `SaveCustomizationUseCase` to persist the final configuration.

*   **Task 3.4: Setup Navigation**
    *   **File:** `res/navigation/nav_graph.xml`
    *   **Details:** Use the Navigation Component to define the action to navigate from `BatteryGalleryFragment` to `CustomizeFragment`, passing the selected `styleId` as an argument.

---


### Phase 4: The Overlay Service

**Goal:** Implement the core functionality: displaying the custom battery as a system overlay and ensuring it doesn't block system UI gestures, using the most modern and least intrusive method available for the user's OS version.

*   **Task 4.1: Implement the Custom View**
    *   **File:** `presentation/overlay/emoji_battery_view.kt`
    *   **(No change)** Create the custom `View` class with `onDraw` logic and methods to update its state.

*   **Task 4.2: Implement the Accessibility Service with Conditional Overlay Type**
    *   **File:** `presentation/overlay/emoji_battery_accessibility_service.kt`
    *   **Details:**
        *   This service is the heart of the overlay feature.
        *   It will add the `EmojiBatteryView` to the `WindowManager`.
        *   **Crucial Change:** The `WindowManager.LayoutParams` will be configured conditionally.
            ```kotlin
            private fun getOverlayLayoutParams(): WindowManager.LayoutParams {
                val overlayType: Int = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                } else {
                    // For older versions, TYPE_PHONE is a common choice, but requires the permission.
                    WindowManager.LayoutParams.TYPE_PHONE 
                }
                // ... set other flags like FLAG_NOT_FOCUSABLE, FLAG_NOT_TOUCH_MODAL, etc.
                return WindowManager.LayoutParams(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    overlayType,
                    // ... flags
                    PixelFormat.TRANSLUCENT
                )
            }
            ```
        *   It will override `onGesture()` to detect swipe-down gestures and programmatically expand the system's notification panel.
        *   It must handle its lifecycle correctly, adding the view on `onServiceConnected` and removing it on `onUnbind`.

*   **Task 4.3: Implement Battery Level Receiver**
    *   **File:** `presentation/overlay/battery_level_receiver.kt`
    *   **(No change)** A `BroadcastReceiver` that listens for `Intent.ACTION_BATTERY_CHANGED` and communicates the new level to the running service.

*   **Task 4.4: Implement Conditional Permission and Service Management**
    *   **Details:** This task is now more nuanced. The logic within the `CustomizeFragment` (on "Apply") and `BatteryGalleryFragment` (global toggle) will be:
        1.  **Check Accessibility Permission:** First, always check if the Accessibility Service is enabled. If not, guide the user to enable it.
        2.  **Check OS Version and Overlay Permission:**
            *   `if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O)`: After verifying Accessibility, also check for the `SYSTEM_ALERT_WINDOW` permission. If it's not granted, guide the user to the appropriate settings screen.
            *   `else`: No need to check for the overlay permission.
        3.  **Start Service:** Only after all *required* permissions for the specific OS version are granted, start the `EmojiBatteryAccessibilityService`.
        4.  **Stop Service:** Provide logic to stop the service when the user disables the feature.


### Phase 5: Monetization & Polish

**Goal:** Integrate ads and the premium content flow.

*   **Task 5.1: Integrate Banner Ads**
    *   **Details:** Add the Google Mobile Ads SDK. Place `AdView` components in `fragment_battery_gallery.xml` and `fragment_customize.xml`.

*   **Task 5.2: Implement Premium Unlock Flow**
    *   **Details:**
        *   In the `BatteryStyleAdapter`, if a premium item is clicked, show a dialog or navigate to a screen offering unlock options (e.g., "Watch Ad" or "Purchase").
        *   In `CustomizeFragment`, if the user tries to "Apply" a configuration that uses a premium asset, initiate the same unlock flow.
        *   Integrate Rewarded Video Ads for the "Watch Ad" option.

*   **Task 5.3: Implement Ad Removal**
    *   **Details:** Implement the "ADS ON/OFF" toggle. This will likely involve an in-app purchase (using the Google Play Billing Library) that, when completed, sets a flag in `DataStore` to hide all ads.This plan is divided into lean, independently compilable, and testable phases. It follows Clean Architecture (Presentation, Domain, Data layers), MVI, and the specified coding conventions.



**Goal:** Integrate ads and the premium content flow.

*   **Task 5.1: Integrate Banner Ads**
    *   **Details:** Add the Google Mobile Ads SDK. Place `AdView` components in `fragment_battery_gallery.xml` and `fragment_customize.xml`.

*   **Task 5.2: Implement Premium Unlock Flow**
    *   **Details:**
        *   In the `BatteryStyleAdapter`, if a premium item is clicked, show a dialog or navigate to a screen offering unlock options (e.g., "Watch Ad" or "Purchase").
        *   In `CustomizeFragment`, if the user tries to "Apply" a configuration that uses a premium asset, initiate the same unlock flow.
        *   Integrate Rewarded Video Ads for the "Watch Ad" option.

*   **Task 5.3: Implement Ad Removal**
    *   **Details:** Implement the "ADS ON/OFF" toggle. This will likely involve an in-app purchase (using the Google Play Billing Library) that, when completed, sets a flag in `DataStore` to hide all ads.